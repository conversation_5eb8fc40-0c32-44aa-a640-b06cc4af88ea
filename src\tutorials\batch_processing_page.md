# Automated Multi-Image Analysis

## 🚀 What is Batch Processing?
Batch Processing allows you to automatically analyze multiple images using the same parameters and methods. Perfect for processing large datasets with consistent analysis workflows, ensuring reproducible results across your entire image collection.

Supported Analysis Types: Grain Size Analysis, Trainable Segmentation, and Advanced Segmentation with FastSAM or MobileSAM models.

## 📋 How to Use Batch Processing
1. Load Images in Project Hub: Import multiple images into your project using the "Import Images" button. Supports TIFF, PNG, JPG formats.
2. Select Images for Processing: In the Project Hub, select multiple images from your gallery that you want to process together.
3. Click "Batch Process": With images selected, click the "Batch Process" button to open the Batch Processing page.
4. Choose Analysis Type: Select from Grain Size Analysis, Trainable Segmentation, or Advanced Segmentation based on your research needs.
5. Configure Parameters: Set segmentation method (FastSAM/MobileSAM), scale factors, detection thresholds, and quality control settings.
6. Start Processing: Click "Start Batch" to begin automated processing. Monitor progress with real-time updates and logs.

## ⚙️ Available Analysis Methods
* Grain Size Analysis: Automated grain detection and size measurement using FastSAM or MobileSAM models
* Trainable Segmentation: Apply custom trained models for specialized segmentation tasks
* Advanced Segmentation: High-precision segmentation with configurable parameters and artifact detection
* Scale Factor Support: Uniform scaling or individual scale factors per image for accurate measurements
* Intelligent Artifact Detection: Automatic removal of border artifacts and duplicate segments
* Quality Control: Configurable thresholds for IoU, stability scores, and minimum region areas

## 📊 Monitoring & Results
* Real-time Progress: Live progress bars showing current image and overall completion status
* Detailed Logging: Comprehensive logs with processing details, errors, and performance metrics
* Error Handling: Automatic error recovery with detailed error reporting for failed images
* Results Export: Export processing results to CSV files and generate comprehensive reports
* Stop/Resume: Ability to stop processing at any time and resume later
* Resource Management: Optimized memory usage and GPU/CPU utilization

## 💡 Pro Tips:
* Test parameters on a single image first before batch processing
* Use MobileSAM for GPU acceleration, FastSAM for CPU-only systems
* Enable artifact detection for cleaner segmentation results
* Save processing logs for reproducibility and troubleshooting