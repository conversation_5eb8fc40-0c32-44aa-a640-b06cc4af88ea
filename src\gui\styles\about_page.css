/* General styles */
.tutorial-container {
    font-family: 'Segoe UI', Arial, sans-serif;
    line-height: 1.6;
    padding: 25px;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    max-width: 1200px;
    margin: 0 auto;
    border-radius: 8px;
}
.tutorial-header {
    color: var(--text-header);
    border-bottom: 3px solid var(--accent-color);
    padding-bottom: 10px;
    margin-bottom: 20px;
    font-size: 2em;
}
.tutorial-section {
    margin-bottom: 30px;
}
.tutorial-subheader {
    color: var(--text-header);
    font-size: 1.5em;
    margin-bottom: 15px;
}
.step-content p, .step-content ul {
    color: var(--text-primary);
}
.overview-section {
    background-color: var(--bg-secondary);
    padding: 20px;
    border-radius: 8px;
    border-left: 5px solid var(--accent-color);
    margin-bottom: 25px;
}
.feature-list {
    list-style-type: none;
    padding-left: 0;
}
.feature-list li {
    position: relative;
    padding-left: 25px;
    margin-bottom: 10px;
}
.feature-list li:before {
    content: '✔';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}
.analysis-cards {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}
.analysis-card {
    background-color: var(--bg-card);
    border: 1px solid var(--border-light);
    border-radius: 8px;
    padding: 20px;
    flex: 1;
    min-width: 280px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}
.analysis-title {
    font-size: 1.2em;
    font-weight: bold;
    color: var(--accent-color);
    margin-bottom: 10px;
}
.workflow-steps {
    list-style-type: none;
    padding-left: 0;
    counter-reset: step-counter;
}
.workflow-step {
    position: relative;
    padding: 15px;
    margin-bottom: 15px;
    background-color: var(--bg-card);
    border-left: 4px solid var(--accent-color);
    border-radius: 5px;
}
.step-number {
    position: absolute;
    top: 15px;
    left: -25px;
    background: var(--accent-color);
    color: var(--text-on-accent);
    border-radius: 50%;
    width: 30px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    font-weight: bold;
}
.step-content strong {
    font-weight: bold;
    color: var(--text-header);
}
code {
    background-color: var(--bg-code);
    color: var(--text-code);
    padding: 2px 5px;
    border-radius: 4px;
    font-family: 'Courier New', Courier, monospace;
}