# src/gui/ui/settings_page_ui.py

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QGroupBox, QScrollArea, QFrame, QTabWidget, QSlider, QSpinBox, QDoubleSpinBox,
    QFormLayout, QCheckBox, QComboBox, QLineEdit, QTextEdit, QRadioButton, QProgressBar,
    QTableWidget, QTableWidgetItem, QSplitter, QToolTip, QApplication)
from PySide6.QtCore import Qt, QSize, QTimer, Signal
from PySide6.QtGui import QFont, QIcon, QPalette

from src.widgets.scrollable_frame import ScrollableFrame

class SettingsPageUI:
    """Class for creating and managing the settings page UI with enhanced user experience."""
    
    # Signal for when settings are changed
    settings_changed = Signal()

    def update_artifact_sensitivities(self):
        """Updates the artifact sensitivity values based on the selected preset."""
        preset = self.default_artifact_preset.currentText()

        if preset == "Conservative":
            self.default_artifact_sensitivity.setValue(0.3)
            self.default_duplicate_sensitivity.setValue(0.8)
        elif preset == "Balanced":
            self.default_artifact_sensitivity.setValue(0.5)
            self.default_duplicate_sensitivity.setValue(0.7)
        elif preset == "Aggressive":
            self.default_artifact_sensitivity.setValue(0.7)
            self.default_duplicate_sensitivity.setValue(0.6)

    def update_theme_preview(self):
        """Updates the theme preview based on selected options."""
        try:
            from src.gui.styles.theme_config import apply_theme, UI_STYLE_PARAMS

            # Get selected theme options
            theme_base = self.theme_combo.currentText().lower().replace(" theme", "")
            color_scheme = self.color_scheme_combo.currentText().lower()
            font_family = self.font_family_combo.currentText()

            # Determine font size
            font_size_name = self.font_size_combo.currentText()
            try:
                from src.gui.styles.theme_config import FONT_SIZES
                font_size = FONT_SIZES.get(font_size_name, 10)
            except ImportError:
                # Default font sizes if import fails
                font_sizes = {"small": 8, "normal": 10, "medium": 12, "large": 14, "extra-large": 16}
                font_size = font_sizes.get(font_size_name, 10)

            # Construct theme name
            theme_name = f"{color_scheme}-{theme_base}"

            # Get UI style parameters from sliders if they exist
            style_params = UI_STYLE_PARAMS.copy()

            if hasattr(self, 'border_radius_slider'):
                style_params["border-radius"] = self.border_radius_slider.value()

            if hasattr(self, 'button_radius_slider'):
                style_params["button-radius"] = self.button_radius_slider.value()

            if hasattr(self, 'padding_slider'):
                style_params["padding"] = self.padding_slider.value()

            if hasattr(self, 'control_height_slider'):
                style_params["control-height"] = self.control_height_slider.value()

            # Section styling parameters
            if hasattr(self, 'section_border_width_spinner'):
                style_params["section-border-width"] = self.section_border_width_spinner.value()

            if hasattr(self, 'section_border_opacity_slider'):
                style_params["section-border-opacity"] = self.section_border_opacity_slider.value()

            if hasattr(self, 'section_border_radius_slider'):
                style_params["section-border-radius"] = self.section_border_radius_slider.value()

            if hasattr(self, 'section_gradient_slider'):
                style_params["section-gradient-strength"] = self.section_gradient_slider.value()

            # Apply theme to preview frame with custom style parameters
            apply_theme(self.theme_preview_frame, theme_name, font_family, font_size, style_params)
        except Exception as e:
            print(f"Error updating theme preview: {e}")

    def setup_settings_page(self):
        """Sets up the Settings page with enhanced UI experience."""
        self.settings_page = QWidget()
        settings_layout = QVBoxLayout(self.settings_page)
        settings_layout.setContentsMargins(0, 0, 0, 0)
        settings_layout.setSpacing(0)
        self.stacked_widget.addTab(self.settings_page, "Settings")

        # Use a scrollable frame for settings content
        settings_scroll = ScrollableFrame()
        settings_layout.addWidget(settings_scroll)

        # Main settings container
        settings_container = QWidget()
        settings_container_layout = QVBoxLayout(settings_container)
        settings_container_layout.setContentsMargins(10, 10, 10, 10)
        settings_container_layout.setSpacing(8)

        # Get the content frame and set the settings container as its child
        content_frame = settings_scroll.get_content_frame()
        content_frame_layout = QVBoxLayout(content_frame)
        content_frame_layout.setContentsMargins(0, 0, 0, 0)
        content_frame_layout.setSpacing(0)
        content_frame_layout.addWidget(settings_container)

        # Create enhanced tab widget with better styling
        settings_tabs = QTabWidget()
        settings_tabs.setTabPosition(QTabWidget.TabPosition.North)
        settings_tabs.setMovable(False)
        settings_tabs.setDocumentMode(True)
        settings_container_layout.addWidget(settings_tabs)
        
        # ===== APPEARANCE SETTINGS =====
        appearance_widget = QWidget()
        appearance_layout = QVBoxLayout(appearance_widget)
        appearance_layout.setSpacing(5)
        appearance_layout.setContentsMargins(5, 5, 5, 5)
        settings_tabs.addTab(appearance_widget, "Appearance")

        # Theme Settings with enhanced styling
        theme_settings_group = QGroupBox("Theme Settings")
        theme_settings_group.setToolTip("Customize the visual appearance of the application")
        theme_settings_layout = QFormLayout(theme_settings_group)
        theme_settings_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        theme_settings_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        # Theme selection
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["Dark Theme", "Light Theme", "System Default"])
        theme_settings_layout.addRow("Theme:", self.theme_combo)

        # Color Scheme
        self.color_scheme_combo = QComboBox()
        try:
            # Get all color schemes from theme_config
            from src.gui.styles.theme_config import COLOR_SCHEMES
            # Extract unique color scheme prefixes (default, blue, green, purple, etc.)
            schemes = set()
            for scheme in COLOR_SCHEMES.keys():
                prefix = scheme.split('-')[0]
                schemes.add(prefix.capitalize())
            self.color_scheme_combo.addItems(sorted(list(schemes)))
        except ImportError:
            self.color_scheme_combo.addItems(["Default", "Blue", "Green", "Purple", "Orange", "Teal", "Monochrome"])
        theme_settings_layout.addRow("Color Scheme:", self.color_scheme_combo)

        # Font Family
        self.font_family_combo = QComboBox()
        try:
            from src.gui.styles.theme_config import FONT_FAMILIES
            self.font_family_combo.addItems(FONT_FAMILIES)
        except ImportError:
            self.font_family_combo.addItems(["Segoe UI", "Arial", "Helvetica", "System"])
        theme_settings_layout.addRow("Font Family:", self.font_family_combo)

        # Font Size
        self.font_size_combo = QComboBox()
        try:
            from src.gui.styles.theme_config import FONT_SIZES
            self.font_size_combo.addItems(list(FONT_SIZES.keys()))
        except ImportError:
            self.font_size_combo.addItems(["small", "normal", "medium", "large", "extra-large"])
        theme_settings_layout.addRow("Font Size:", self.font_size_combo)



        appearance_layout.addWidget(theme_settings_group)

        # UI Style Customization with enhanced layout
        style_settings_group = QGroupBox("UI Style Customization")
        style_settings_group.setToolTip("Fine-tune the visual styling of UI elements")
        style_settings_layout = QFormLayout(style_settings_group)
        style_settings_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        style_settings_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)

        # Border Radius Slider
        border_radius_layout = QHBoxLayout()
        self.border_radius_slider = QSlider(Qt.Orientation.Horizontal)
        self.border_radius_slider.setRange(0, 12)
        self.border_radius_slider.setValue(4)
        self.border_radius_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.border_radius_slider.setTickInterval(2)

        self.border_radius_value = QLabel("4")
        border_radius_layout.addWidget(self.border_radius_slider)
        border_radius_layout.addWidget(self.border_radius_value)
        style_settings_layout.addRow("Border Radius:", border_radius_layout)

        # Button Radius Slider
        button_radius_layout = QHBoxLayout()
        self.button_radius_slider = QSlider(Qt.Orientation.Horizontal)
        self.button_radius_slider.setRange(0, 16)
        self.button_radius_slider.setValue(6)
        self.button_radius_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.button_radius_slider.setTickInterval(2)

        self.button_radius_value = QLabel("6")
        button_radius_layout.addWidget(self.button_radius_slider)
        button_radius_layout.addWidget(self.button_radius_value)
        style_settings_layout.addRow("Button Radius:", button_radius_layout)

        # Padding Slider
        padding_layout = QHBoxLayout()
        self.padding_slider = QSlider(Qt.Orientation.Horizontal)
        self.padding_slider.setRange(2, 12)
        self.padding_slider.setValue(6)
        self.padding_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.padding_slider.setTickInterval(2)

        self.padding_value = QLabel("6")
        padding_layout.addWidget(self.padding_slider)
        padding_layout.addWidget(self.padding_value)
        style_settings_layout.addRow("Padding:", padding_layout)

        # Control Height Slider
        control_height_layout = QHBoxLayout()
        self.control_height_slider = QSlider(Qt.Orientation.Horizontal)
        self.control_height_slider.setRange(20, 40)
        self.control_height_slider.setValue(28)
        self.control_height_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.control_height_slider.setTickInterval(4)

        self.control_height_value = QLabel("28")
        control_height_layout.addWidget(self.control_height_slider)
        control_height_layout.addWidget(self.control_height_value)
        style_settings_layout.addRow("Control Height:", control_height_layout)

        # Section Styling Group with enhanced layout
        section_style_group = QGroupBox("Section Styling")
        section_style_group.setToolTip("Customize the appearance of grouped sections")
        section_style_layout = QFormLayout(section_style_group)
        section_style_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)

        # Section Border Width Spinner
        section_border_width_layout = QHBoxLayout()
        self.section_border_width_spinner = QSpinBox()
        self.section_border_width_spinner.setRange(1, 3)
        self.section_border_width_spinner.setSingleStep(1)
        self.section_border_width_spinner.setValue(1)  # Default thin border

        section_border_width_layout.addWidget(self.section_border_width_spinner)
        section_style_layout.addRow("Border Width (px):", section_border_width_layout)

        # Section Border Opacity Slider
        section_border_opacity_layout = QHBoxLayout()
        self.section_border_opacity_slider = QSlider(Qt.Orientation.Horizontal)
        self.section_border_opacity_slider.setRange(0, 100)
        self.section_border_opacity_slider.setValue(30)  # Default 30% opacity
        self.section_border_opacity_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.section_border_opacity_slider.setTickInterval(10)

        self.section_border_opacity_value = QLabel("30")
        section_border_opacity_layout.addWidget(self.section_border_opacity_slider)
        section_border_opacity_layout.addWidget(self.section_border_opacity_value)
        section_style_layout.addRow("Border Opacity (%):", section_border_opacity_layout)

        # Section Border Radius Slider
        section_border_radius_layout = QHBoxLayout()
        self.section_border_radius_slider = QSlider(Qt.Orientation.Horizontal)
        self.section_border_radius_slider.setRange(0, 8)
        self.section_border_radius_slider.setValue(3)  # Default reduced to 3px
        self.section_border_radius_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.section_border_radius_slider.setTickInterval(1)

        self.section_border_radius_value = QLabel("3")
        section_border_radius_layout.addWidget(self.section_border_radius_slider)
        section_border_radius_layout.addWidget(self.section_border_radius_value)
        section_style_layout.addRow("Border Radius:", section_border_radius_layout)

        # Section Gradient Strength Slider
        section_gradient_layout = QHBoxLayout()
        self.section_gradient_slider = QSlider(Qt.Orientation.Horizontal)
        self.section_gradient_slider.setRange(0, 5)
        self.section_gradient_slider.setValue(1)  # Default reduced to 1
        self.section_gradient_slider.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.section_gradient_slider.setTickInterval(0.5)

        self.section_gradient_value = QLabel("1")
        section_gradient_layout.addWidget(self.section_gradient_slider)
        section_gradient_layout.addWidget(self.section_gradient_value)
        section_style_layout.addRow("Gradient Strength:", section_gradient_layout)

        style_settings_layout.addRow("", section_style_group)

        # Connect slider signals
        self.border_radius_slider.valueChanged.connect(lambda v: self.border_radius_value.setText(str(v)))
        self.button_radius_slider.valueChanged.connect(lambda v: self.button_radius_value.setText(str(v)))
        self.padding_slider.valueChanged.connect(lambda v: self.padding_value.setText(str(v)))
        self.control_height_slider.valueChanged.connect(lambda v: self.control_height_value.setText(str(v)))
        self.section_border_radius_slider.valueChanged.connect(lambda v: self.section_border_radius_value.setText(str(v)))
        self.section_gradient_slider.valueChanged.connect(lambda v: self.section_gradient_value.setText(str(v)))
        self.section_border_opacity_slider.valueChanged.connect(lambda v: self.section_border_opacity_value.setText(str(v)))

        # Connect all sliders to update preview
        self.border_radius_slider.valueChanged.connect(self.update_theme_preview)
        self.button_radius_slider.valueChanged.connect(self.update_theme_preview)
        self.padding_slider.valueChanged.connect(self.update_theme_preview)
        self.control_height_slider.valueChanged.connect(self.update_theme_preview)
        self.section_border_width_spinner.valueChanged.connect(self.update_theme_preview)
        self.section_border_opacity_slider.valueChanged.connect(self.update_theme_preview)
        self.section_border_radius_slider.valueChanged.connect(self.update_theme_preview)
        self.section_gradient_slider.valueChanged.connect(self.update_theme_preview)

        appearance_layout.addWidget(style_settings_group)

        # Theme Preview with enhanced styling
        theme_preview_group = QGroupBox("Live Theme Preview")
        theme_preview_group.setToolTip("See how your theme changes will look in real-time")
        theme_preview_layout = QVBoxLayout(theme_preview_group)

        self.theme_preview_frame = QFrame()
        self.theme_preview_frame.setFrameShape(QFrame.Shape.StyledPanel)
        self.theme_preview_frame.setMinimumHeight(350)

        preview_layout = QVBoxLayout(self.theme_preview_frame)

        # Title and description
        preview_title = QLabel("Theme Preview")
        preview_title.setStyleSheet("font-weight: bold; font-size: 14px;")
        preview_layout.addWidget(preview_title)

        preview_label = QLabel("This is a preview of your selected theme.")
        preview_layout.addWidget(preview_label)

        # Create a tab widget for the preview to show more UI elements
        preview_tabs = QTabWidget()
        preview_layout.addWidget(preview_tabs)

        # Basic Controls Tab
        basic_controls = QWidget()
        basic_layout = QFormLayout(basic_controls)
        preview_tabs.addTab(basic_controls, "Basic Controls")

        # Add various UI elements
        preview_line_edit = QLineEdit("Sample text input")
        basic_layout.addRow("Text Input:", preview_line_edit)

        preview_combo = QComboBox()
        preview_combo.addItems(["Option 1", "Option 2", "Option 3"])
        basic_layout.addRow("Dropdown:", preview_combo)

        preview_spinbox = QSpinBox()
        preview_spinbox.setRange(0, 100)
        preview_spinbox.setValue(50)
        basic_layout.addRow("Spin Box:", preview_spinbox)

        preview_double_spinbox = QDoubleSpinBox()
        preview_double_spinbox.setRange(0, 1.0)
        preview_double_spinbox.setSingleStep(0.1)
        preview_double_spinbox.setValue(0.5)
        basic_layout.addRow("Double Spin Box:", preview_double_spinbox)

        preview_checkbox = QCheckBox("Enabled")
        preview_checkbox.setChecked(True)
        basic_layout.addRow("Checkbox:", preview_checkbox)

        preview_radio1 = QRadioButton("Option 1")
        preview_radio1.setChecked(True)
        preview_radio2 = QRadioButton("Option 2")
        radio_layout = QHBoxLayout()
        radio_layout.addWidget(preview_radio1)
        radio_layout.addWidget(preview_radio2)
        radio_widget = QWidget()
        radio_widget.setLayout(radio_layout)
        basic_layout.addRow("Radio Buttons:", radio_widget)

        preview_slider = QSlider(Qt.Orientation.Horizontal)
        preview_slider.setValue(50)
        basic_layout.addRow("Slider:", preview_slider)

        # Advanced Controls Tab
        advanced_controls = QWidget()
        advanced_layout = QVBoxLayout(advanced_controls)
        preview_tabs.addTab(advanced_controls, "Advanced Controls")

        # Progress bar
        progress_group = QGroupBox("Progress Indicators")
        progress_layout = QVBoxLayout(progress_group)

        preview_progress = QProgressBar()
        preview_progress.setValue(70)
        progress_layout.addWidget(preview_progress)

        advanced_layout.addWidget(progress_group)

        # Table view
        table_group = QGroupBox("Data Views")
        table_layout = QVBoxLayout(table_group)

        # Simple table for preview
        preview_table = QTableWidget(3, 3)
        preview_table.setHorizontalHeaderLabels(["Column 1", "Column 2", "Column 3"])
        for row in range(3):
            for col in range(3):
                preview_table.setItem(row, col, QTableWidgetItem(f"Item {row+1},{col+1}"))

        table_layout.addWidget(preview_table)
        advanced_layout.addWidget(table_group)

        # Buttons Tab
        buttons_tab = QWidget()
        buttons_layout = QVBoxLayout(buttons_tab)
        preview_tabs.addTab(buttons_tab, "Buttons")

        # Regular buttons
        regular_buttons = QGroupBox("Regular Buttons")
        regular_buttons_layout = QHBoxLayout(regular_buttons)

        preview_button1 = QPushButton("Primary Button")
        regular_buttons_layout.addWidget(preview_button1)

        preview_button2 = QPushButton("Secondary Button")
        regular_buttons_layout.addWidget(preview_button2)

        disabled_button = QPushButton("Disabled Button")
        disabled_button.setEnabled(False)
        regular_buttons_layout.addWidget(disabled_button)

        buttons_layout.addWidget(regular_buttons)

        # Special buttons (like those in trainable segmentation)
        special_buttons = QGroupBox("Special Buttons")
        special_buttons_layout = QHBoxLayout(special_buttons)

        draw_button = QPushButton("Draw Label")
        special_buttons_layout.addWidget(draw_button)

        erase_button = QPushButton("Erase Label")
        special_buttons_layout.addWidget(erase_button)

        magic_wand_button = QPushButton("Magic Wand")
        special_buttons_layout.addWidget(magic_wand_button)

        buttons_layout.addWidget(special_buttons)

        theme_preview_layout.addWidget(self.theme_preview_frame)
        appearance_layout.addWidget(theme_preview_group)

        # Language Settings with enhanced styling
        language_settings_group = QGroupBox("🌐 Language Settings")
        language_settings_group.setToolTip("Configure application language preferences")
        language_settings_layout = QFormLayout(language_settings_group)
        language_settings_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)

        # Language selection
        self.language_combo = QComboBox()
        self.language_combo.addItems(["English", "French", "Spanish", "German"])
        language_settings_layout.addRow("Language:", self.language_combo)

        appearance_layout.addWidget(language_settings_group)

        # Apply Theme button removed - functionality merged with Save Settings button

        # Connect theme change signals to update preview
        self.theme_combo.currentIndexChanged.connect(self.update_theme_preview)
        self.color_scheme_combo.currentIndexChanged.connect(self.update_theme_preview)
        self.font_family_combo.currentIndexChanged.connect(self.update_theme_preview)
        self.font_size_combo.currentIndexChanged.connect(self.update_theme_preview)

        # ===== UNSUPERVISED SEGMENTATION =====
        unsupervised_seg_widget = QWidget()
        unsupervised_seg_layout = QVBoxLayout(unsupervised_seg_widget)
        unsupervised_seg_layout.setSpacing(5)
        unsupervised_seg_layout.setContentsMargins(5, 5, 5, 5)
        settings_tabs.addTab(unsupervised_seg_widget, "Unsupervised Segmentation")

        # Segmentation Settings
        seg_settings_group = QGroupBox("Segmentation Settings")
        seg_settings_group.setToolTip("Configure parameters for the unsupervised segmentation model")
        seg_settings_group.setContentsMargins(5, 3, 5, 3)
        seg_settings_layout = QFormLayout(seg_settings_group)
        seg_settings_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        seg_settings_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        seg_settings_layout.setVerticalSpacing(3)
        seg_settings_layout.setContentsMargins(3, 3, 3, 3)

        # Default segmentation method
        self.default_seg_method = QComboBox()
        self.default_seg_method.addItems(["KMeans", "Felzenszwalb", "PCA"])
        seg_settings_layout.addRow("Default Method:", self.default_seg_method)

        # Default parameters
        self.default_epochs = QSpinBox()
        self.default_epochs.setRange(1, 1000)
        self.default_epochs.setValue(100)
        seg_settings_layout.addRow("Default Epochs:", self.default_epochs)

        self.default_min_labels = QSpinBox()
        self.default_min_labels.setRange(1, 50)
        self.default_min_labels.setValue(3)
        seg_settings_layout.addRow("Default Min Labels:", self.default_min_labels)

        self.default_max_labels = QSpinBox()
        self.default_max_labels.setRange(1, 300)
        self.default_max_labels.setValue(10)
        seg_settings_layout.addRow("Default Max Labels:", self.default_max_labels)

        unsupervised_seg_layout.addWidget(seg_settings_group)

        # Color Palette Settings
        color_settings_group = QGroupBox("Color Palette Settings")
        color_settings_group.setToolTip("Manage and select color palettes for segmentation")
        color_settings_group.setContentsMargins(5, 3, 5, 3)
        color_settings_layout = QFormLayout(color_settings_group)
        color_settings_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        color_settings_layout.setVerticalSpacing(3)
        color_settings_layout.setContentsMargins(3, 3, 3, 3)

        # Default color palette
        self.default_color_palette = QComboBox()
        self.default_color_palette.addItems(["Default", "Vibrant", "Pastel", "Grayscale", "High Contrast"])
        color_settings_layout.addRow("Default Color Palette:", self.default_color_palette)

        unsupervised_seg_layout.addWidget(color_settings_group)

        # Image Settings
        img_settings_group = QGroupBox("Image Settings")
        img_settings_group.setToolTip("Set default image processing parameters")
        img_settings_group.setContentsMargins(5, 3, 5, 3)
        img_settings_layout = QFormLayout(img_settings_group)
        img_settings_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        img_settings_layout.setVerticalSpacing(3)
        img_settings_layout.setContentsMargins(3, 3, 3, 3)

        # Default image size
        self.default_img_width = QSpinBox()
        self.default_img_width.setRange(100, 2000)
        self.default_img_width.setValue(750)
        img_settings_layout.addRow("Default Width:", self.default_img_width)

        self.default_img_height = QSpinBox()
        self.default_img_height.setRange(100, 2000)
        self.default_img_height.setValue(750)
        img_settings_layout.addRow("Default Height:", self.default_img_height)

        # Auto-resize images
        self.auto_resize = QCheckBox("Auto-resize images to default size")
        self.auto_resize.setChecked(True)
        img_settings_layout.addRow("", self.auto_resize)

        unsupervised_seg_layout.addWidget(img_settings_group)

        # ===== TRAINABLE SEGMENTATION =====
        trainable_seg_widget = QWidget()
        trainable_seg_layout = QVBoxLayout(trainable_seg_widget)
        trainable_seg_layout.setSpacing(5)
        trainable_seg_layout.setContentsMargins(5, 5, 5, 5)
        settings_tabs.addTab(trainable_seg_widget, "Trainable Segmentation")

        # Feature Settings
        feature_settings_group = QGroupBox("Feature Settings")
        feature_settings_group.setToolTip("Configure features for the trainable segmentation model")
        feature_settings_group.setContentsMargins(5, 3, 5, 3)
        feature_settings_layout = QFormLayout(feature_settings_group)
        feature_settings_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        feature_settings_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        feature_settings_layout.setVerticalSpacing(3)
        feature_settings_layout.setContentsMargins(3, 3, 3, 3)

        # Default feature selection
        self.default_intensity_features = QCheckBox("Intensity Features")
        self.default_intensity_features.setChecked(True)
        feature_settings_layout.addRow("", self.default_intensity_features)

        self.default_edge_features = QCheckBox("Edge Features")
        self.default_edge_features.setChecked(False)
        feature_settings_layout.addRow("", self.default_edge_features)

        self.default_texture_features = QCheckBox("Texture Features")
        self.default_texture_features.setChecked(True)
        feature_settings_layout.addRow("", self.default_texture_features)

        # Sigma parameters
        self.default_sigma_min = QSpinBox()
        self.default_sigma_min.setRange(1, 10)
        self.default_sigma_min.setValue(1)
        feature_settings_layout.addRow("Sigma Min:", self.default_sigma_min)

        self.default_sigma_max = QSpinBox()
        self.default_sigma_max.setRange(2, 32)
        self.default_sigma_max.setValue(16)
        feature_settings_layout.addRow("Sigma Max:", self.default_sigma_max)

        trainable_seg_layout.addWidget(feature_settings_group)

        # Classifier Settings
        classifier_settings_group = QGroupBox("Classifier Settings")
        classifier_settings_group.setToolTip("Set parameters for the Random Forest classifier")
        classifier_settings_group.setContentsMargins(5, 3, 5, 3)
        classifier_settings_layout = QFormLayout(classifier_settings_group)
        classifier_settings_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        classifier_settings_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        classifier_settings_layout.setVerticalSpacing(3)
        classifier_settings_layout.setContentsMargins(3, 3, 3, 3)

        self.default_n_estimators = QSpinBox()
        self.default_n_estimators.setRange(10, 1000)
        self.default_n_estimators.setValue(100)
        classifier_settings_layout.addRow("Number of Trees:", self.default_n_estimators)

        self.default_max_depth = QSpinBox()
        self.default_max_depth.setRange(1, 100)
        self.default_max_depth.setValue(10)
        classifier_settings_layout.addRow("Max Depth:", self.default_max_depth)

        self.default_max_samples = QDoubleSpinBox()
        self.default_max_samples.setRange(0.1, 1.0)
        self.default_max_samples.setSingleStep(0.1)
        self.default_max_samples.setValue(0.7)
        classifier_settings_layout.addRow("Max Samples:", self.default_max_samples)

        trainable_seg_layout.addWidget(classifier_settings_group)

        # Drawing Settings
        drawing_settings_group = QGroupBox("Drawing Settings")
        drawing_settings_group.setToolTip("Customize drawing tool settings for annotations")
        drawing_settings_group.setContentsMargins(5, 3, 5, 3)
        drawing_settings_layout = QFormLayout(drawing_settings_group)
        drawing_settings_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        drawing_settings_layout.setVerticalSpacing(3)
        drawing_settings_layout.setContentsMargins(3, 3, 3, 3)

        self.default_brush_size = QSpinBox()
        self.default_brush_size.setRange(1, 50)
        self.default_brush_size.setValue(5)
        drawing_settings_layout.addRow("Default Brush Size:", self.default_brush_size)

        trainable_seg_layout.addWidget(drawing_settings_group)

        # ===== GRAIN ANALYSIS SETTINGS =====
        grain_analysis_widget = QWidget()
        grain_analysis_layout = QVBoxLayout(grain_analysis_widget)
        grain_analysis_layout.setSpacing(5)
        grain_analysis_layout.setContentsMargins(5, 5, 5, 5)
        settings_tabs.addTab(grain_analysis_widget, "Grain Analysis")

        # Create a scroll area to contain all settings
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setSpacing(5)  # Spacing between groups
        scroll_layout.setContentsMargins(5, 5, 5, 5)  # Reduced margins
        scroll_area.setWidget(scroll_content)
        grain_analysis_layout.addWidget(scroll_area)

        # Segmentation Parameters
        grain_seg_group = QGroupBox("Segmentation Parameters")
        grain_seg_group.setToolTip("Parameters for the grain segmentation model")
        grain_seg_group.setContentsMargins(5, 3, 5, 3)
        grain_seg_layout = QFormLayout(grain_seg_group)
        grain_seg_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        grain_seg_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        grain_seg_layout.setVerticalSpacing(3)
        grain_seg_layout.setContentsMargins(3, 3, 3, 3)

        self.default_input_size = QSpinBox()
        self.default_input_size.setRange(512, 2048)
        self.default_input_size.setValue(1024)
        self.default_input_size.setSingleStep(128)
        grain_seg_layout.addRow("Default Input Size:", self.default_input_size)

        self.default_iou_threshold = QDoubleSpinBox()
        self.default_iou_threshold.setRange(0.1, 1.0)
        self.default_iou_threshold.setSingleStep(0.05)
        self.default_iou_threshold.setValue(0.7)
        grain_seg_layout.addRow("Default IOU Threshold:", self.default_iou_threshold)

        self.default_conf_threshold = QDoubleSpinBox()
        self.default_conf_threshold.setRange(0.1, 1.0)
        self.default_conf_threshold.setSingleStep(0.05)
        self.default_conf_threshold.setValue(0.5)
        grain_seg_layout.addRow("Default Confidence Threshold:", self.default_conf_threshold)

        self.default_max_det = QSpinBox()
        self.default_max_det.setRange(100, 1000)
        self.default_max_det.setValue(500)
        self.default_max_det.setSingleStep(50)
        grain_seg_layout.addRow("Default Max Detections:", self.default_max_det)

        scroll_layout.addWidget(grain_seg_group)

        # NMS Parameters section has been removed and replaced by Intelligent Artifact Handling

        # Subgrain Filtering with enhanced styling
        subgrain_group = QGroupBox("Subgrain Filtering")
        subgrain_group.setToolTip("Configure filters to identify and handle subgrain structures")
        subgrain_group.setContentsMargins(5, 3, 5, 3)
        subgrain_layout = QFormLayout(subgrain_group)
        subgrain_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        subgrain_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        subgrain_layout.setVerticalSpacing(3)
        subgrain_layout.setContentsMargins(3, 3, 3, 3)

        self.default_angle_threshold = QDoubleSpinBox()
        self.default_angle_threshold.setRange(1.0, 20.0)
        self.default_angle_threshold.setSingleStep(0.5)
        self.default_angle_threshold.setValue(5.0)
        subgrain_layout.addRow("Angle Threshold (°):", self.default_angle_threshold)

        self.default_straight_edge_ratio = QDoubleSpinBox()
        self.default_straight_edge_ratio.setRange(0.0, 1.0)
        self.default_straight_edge_ratio.setSingleStep(0.05)
        self.default_straight_edge_ratio.setValue(0.25)
        subgrain_layout.addRow("Straight Edge Ratio:", self.default_straight_edge_ratio)

        scroll_layout.addWidget(subgrain_group)

        # Artifact Handling Parameters with enhanced styling
        artifact_group = QGroupBox("Intelligent Artifact Handling")
        artifact_group.setToolTip("Configure intelligent detection and removal of imaging artifacts")
        artifact_group.setContentsMargins(5, 3, 5, 3)
        artifact_layout = QFormLayout(artifact_group)
        artifact_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        artifact_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        artifact_layout.setVerticalSpacing(3)
        artifact_layout.setContentsMargins(3, 3, 3, 3)

        # Default preset
        self.default_artifact_preset = QComboBox()
        self.default_artifact_preset.addItems(["Conservative", "Balanced", "Aggressive"])
        self.default_artifact_preset.setCurrentIndex(1)  # Default to Balanced
        artifact_layout.addRow("Default Preset:", self.default_artifact_preset)

        # Default artifact sensitivity
        self.default_artifact_sensitivity = QDoubleSpinBox()
        self.default_artifact_sensitivity.setRange(0.0, 1.0)
        self.default_artifact_sensitivity.setSingleStep(0.05)
        self.default_artifact_sensitivity.setValue(0.5)  # Default to 0.5 (50%)
        artifact_layout.addRow("Artifact Sensitivity:", self.default_artifact_sensitivity)

        # Default duplicate sensitivity
        self.default_duplicate_sensitivity = QDoubleSpinBox()
        self.default_duplicate_sensitivity.setRange(0.0, 1.0)
        self.default_duplicate_sensitivity.setSingleStep(0.05)
        self.default_duplicate_sensitivity.setValue(0.7)  # Default to 0.7 (70%)
        artifact_layout.addRow("Duplicate Sensitivity:", self.default_duplicate_sensitivity)

        # Connect preset combo to update sensitivity values
        self.default_artifact_preset.currentIndexChanged.connect(self.update_artifact_sensitivities)

        scroll_layout.addWidget(artifact_group)

        # MobileSAM Parameters with enhanced styling
        mobilesam_group = QGroupBox("MobileSAM Parameters")
        mobilesam_group.setToolTip("Configure advanced AI segmentation model parameters")
        mobilesam_group.setContentsMargins(5, 3, 5, 3)
        mobilesam_layout_form = QFormLayout(mobilesam_group)
        mobilesam_layout_form.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        mobilesam_layout_form.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        mobilesam_layout_form.setVerticalSpacing(3)
        mobilesam_layout_form.setContentsMargins(3, 3, 3, 3)

        self.default_points_per_side = QSpinBox()
        self.default_points_per_side.setRange(8, 64)
        self.default_points_per_side.setValue(32)
        self.default_points_per_side.setSingleStep(4)
        mobilesam_layout_form.addRow("Points Per Side:", self.default_points_per_side)

        self.default_pred_iou_thresh = QDoubleSpinBox()
        self.default_pred_iou_thresh.setRange(0.5, 0.99)
        self.default_pred_iou_thresh.setSingleStep(0.01)
        self.default_pred_iou_thresh.setValue(0.88)
        mobilesam_layout_form.addRow("Prediction IOU Threshold:", self.default_pred_iou_thresh)

        self.default_stability_score_thresh = QDoubleSpinBox()
        self.default_stability_score_thresh.setRange(0.5, 0.99)
        self.default_stability_score_thresh.setSingleStep(0.01)
        self.default_stability_score_thresh.setValue(0.95)
        mobilesam_layout_form.addRow("Stability Score Threshold:", self.default_stability_score_thresh)

        self.default_box_nms_thresh = QDoubleSpinBox()
        self.default_box_nms_thresh.setRange(0.1, 1.0)
        self.default_box_nms_thresh.setSingleStep(0.05)
        self.default_box_nms_thresh.setValue(0.3)
        mobilesam_layout_form.addRow("Box NMS Threshold:", self.default_box_nms_thresh)

        self.default_min_mask_area = QSpinBox()
        self.default_min_mask_area.setRange(0, 1000)
        self.default_min_mask_area.setValue(0)
        self.default_min_mask_area.setSingleStep(10)
        mobilesam_layout_form.addRow("Min Mask Area:", self.default_min_mask_area)

        scroll_layout.addWidget(mobilesam_group)



        # ===== POINT COUNTING SETTINGS =====
        point_counting_widget = QWidget()
        point_counting_layout = QVBoxLayout(point_counting_widget)
        point_counting_layout.setSpacing(5)
        point_counting_layout.setContentsMargins(5, 5, 5, 5)
        settings_tabs.addTab(point_counting_widget, "Point Counting")

        # Grid Settings with enhanced styling
        grid_settings_group = QGroupBox("Grid Settings")
        grid_settings_group.setToolTip("Configure grid overlay appearance and behavior")
        grid_settings_group.setContentsMargins(5, 3, 5, 3)
        grid_settings_layout = QFormLayout(grid_settings_group)
        grid_settings_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        grid_settings_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        grid_settings_layout.setVerticalSpacing(3)
        grid_settings_layout.setContentsMargins(3, 3, 3, 3)

        # Default grid color
        self.default_grid_color_button = QPushButton()
        self.default_grid_color_button.setFixedSize(80, 24)
        self.default_grid_color_button.setStyleSheet("background-color: rgba(255, 255, 255, 180);")
        self.default_grid_color_button.clicked.connect(self.choose_grid_color)
        grid_settings_layout.addRow("Default Grid Color:", self.default_grid_color_button)

        # Default grid opacity
        self.default_grid_opacity = QSlider(Qt.Orientation.Horizontal)
        self.default_grid_opacity.setRange(10, 100)
        self.default_grid_opacity.setValue(70)
        self.default_grid_opacity.setTickPosition(QSlider.TickPosition.TicksBelow)
        self.default_grid_opacity.setTickInterval(10)
        self.default_grid_opacity_value = QLabel("70")
        opacity_layout = QHBoxLayout()
        opacity_layout.addWidget(self.default_grid_opacity)
        opacity_layout.addWidget(self.default_grid_opacity_value)
        grid_settings_layout.addRow("Default Grid Opacity (%):", opacity_layout)

        # Connect opacity slider to label
        self.default_grid_opacity.valueChanged.connect(lambda v: self.default_grid_opacity_value.setText(str(v)))

        point_counting_layout.addWidget(grid_settings_group)

        # ===== AI ASSISTANT SETTINGS =====
        ai_assistant_widget = QWidget()
        ai_assistant_layout = QVBoxLayout(ai_assistant_widget)
        ai_assistant_layout.setSpacing(5)
        ai_assistant_layout.setContentsMargins(5, 5, 5, 5)
        settings_tabs.addTab(ai_assistant_widget, "AI Assistant")

        # API Settings with enhanced styling
        api_settings_group = QGroupBox("API Settings")
        api_settings_group.setToolTip("Configure AI model and API preferences")
        api_settings_group.setContentsMargins(5, 3, 5, 3)
        api_settings_layout = QFormLayout(api_settings_group)
        api_settings_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        api_settings_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        api_settings_layout.setVerticalSpacing(3)
        api_settings_layout.setContentsMargins(3, 3, 3, 3)

        self.default_gemini_model = QComboBox()
        self.default_gemini_model.addItems(["gemini-2.5-pro-preview-06-05", "gemini-2.5-flash-preview-05-20", "gemini-2.5-flash-preview-04-17", "gemini-2.0-flash", "gemini-2.0-pro", "gemini-1.5-flash", "gemini-1.5-pro"])
        api_settings_layout.addRow("Default Model:", self.default_gemini_model)

        ai_assistant_layout.addWidget(api_settings_group)

        # Prompt Templates with enhanced styling
        prompt_templates_group = QGroupBox("Prompt Templates")
        prompt_templates_group.setToolTip("Store and manage reusable prompt templates for AI assistance")
        prompt_templates_group.setContentsMargins(10, 5, 10, 5)
        prompt_templates_layout = QVBoxLayout(prompt_templates_group)
        prompt_templates_layout.setSpacing(5)
        prompt_templates_layout.setContentsMargins(5, 5, 5, 5)

        prompt_templates_label = QLabel("Store common prompt templates for quick access:")
        prompt_templates_layout.addWidget(prompt_templates_label)

        self.prompt_templates_text = QTextEdit()
        self.prompt_templates_text.setPlaceholderText("Enter prompt templates here, one per line. Example:\nAnalyze the grain size distribution in this image.\nIdentify mineral phases in this thin section.")
        prompt_templates_layout.addWidget(self.prompt_templates_text)

        ai_assistant_layout.addWidget(prompt_templates_group)

        # ===== GENERAL SETTINGS =====
        general_widget = QWidget()
        general_layout = QVBoxLayout(general_widget)
        general_layout.setSpacing(5)
        general_layout.setContentsMargins(5, 5, 5, 5)
        settings_tabs.addTab(general_widget, "General")

        # Export Settings with enhanced styling
        export_settings_group = QGroupBox("Export Settings")
        export_settings_group.setToolTip("Configure default export preferences and file formats")
        export_settings_group.setContentsMargins(5, 3, 5, 3)
        export_settings_layout = QFormLayout(export_settings_group)
        export_settings_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        export_settings_layout.setFieldGrowthPolicy(QFormLayout.FieldGrowthPolicy.ExpandingFieldsGrow)
        export_settings_layout.setVerticalSpacing(3)
        export_settings_layout.setContentsMargins(3, 3, 3, 3)

        # Default export directory
        self.export_dir = QLineEdit()
        self.export_dir.setPlaceholderText("Default export directory")
        export_dir_layout = QHBoxLayout()
        export_dir_layout.addWidget(self.export_dir)
        self.browse_export_dir = QPushButton("Browse...")
        export_dir_layout.addWidget(self.browse_export_dir)
        export_settings_layout.addRow("Export Directory:", export_dir_layout)

        # Default export format
        self.export_format = QComboBox()
        self.export_format.addItems(["PNG", "JPEG", "TIFF", "BMP"])
        export_settings_layout.addRow("Default Format:", self.export_format)

        general_layout.addWidget(export_settings_group)

        # Save and Reset buttons
        buttons_layout = QHBoxLayout()
        self.save_settings_btn = QPushButton("Save and Apply Settings")
        self.reset_settings_btn = QPushButton("Reset to Defaults")
        buttons_layout.addWidget(self.save_settings_btn)
        buttons_layout.addWidget(self.reset_settings_btn)
        settings_container_layout.addLayout(buttons_layout)